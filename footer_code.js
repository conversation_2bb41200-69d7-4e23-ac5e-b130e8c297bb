/// Open SDK function
function saveUTMParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const utmKeys = ["utm_source", "utm_medium", "utm_campaign"];

    let utmData = [];
    let found = false;

    utmKeys.forEach(key => {
        const value = urlParams.get(key);
        if (value) {
            utmData.push(`${key}=${value}`);
            found = true;
        }
    });

    if (found) {
        sessionStorage.setItem("utm_params", utmData.join("&"));
    }
}

function saveOptimisedParam() {
    const urlParams = new URLSearchParams(window.location.search);

    const optimised = urlParams.get("optimised");
    if (optimised) {
        sessionStorage.setItem("optimised", optimised);
    }
}

saveUTMParams();
saveOptimisedParam();

function getUTMParams() {
    return sessionStorage.getItem("utm_params");
}

function getOptimisedParam() {
    const value = sessionStorage.getItem("optimised");

    // Normalize to true/false
    return value === "true";
}

function prepareQuote(productCode) {
    const utmParams = getUTMParams();
    const options = {
        mode: 2,
        customParameters: utmParams ? utmParams : ""
    };

    // Add all UTM parameters as individual properties to options from session storage
    if (utmParams) {
        const structuredUtmParams = new URLSearchParams(utmParams);
        structuredUtmParams.forEach((value, key) => {
            options[key] = value;
        });
    }
  
  	console.log(options);

    window.opensdk.quote
        .prepare(productCode)
        .then(function open(response) {
            window.opensdk.quote.portal(
                response.product_code,
                response.quote_ref,
                options
            );
        })
        .catch(function error(response) {
            console.log(response.error);
        });
}

function loadOpenSdk(optimised) {
    const config = {
        apiEndpoint: 'https://insurance.wanda.world/api',
        portalUrl: 'https://insurance.wanda.world'
    };

    let apiKey = '412636cc-73bd-4c74-9cc6-90db0f9ebfab'; // Wanda SDK key
    if (optimised) {
        // override api key with the optimised landing page key for specific affiliate partners
        console.log("Info: Overriding SDK setup with optimised key");
        apiKey = '0f085fbc-e197-4496-b4dd-9a18070f1344';
    }
    window.opensdk.load(apiKey, config);
}

// Event listeners for quote buttons
$('.quote-button').on('click', function () {
    const optimised = getOptimisedParam();
    loadOpenSdk(optimised);
    prepareQuote('cmp-avs-uk-direct');
});

// Function to add UTM parameters to internal anchor links
function addUTMParamsToInternalLinks() {
    const currentDomain = window.location.hostname;
    const utmParams = getUTMParams();

    if (!utmParams) return; // No UTM params to add

    // Find all anchor links that point to internal pages
    $('a[href]').each(function() {
        const link = $(this);
        const href = link.attr('href');

        // Check if it's an internal link (relative or same domain)
        if (href && (href.startsWith('/') || href.includes(currentDomain))) {
            const url = new URL(href, window.location.origin);

            // Only add UTM params if they're not already present
            const existingParams = new URLSearchParams(url.search);
            const utmParamsObj = new URLSearchParams(utmParams);

            let hasChanges = false;
            utmParamsObj.forEach((value, key) => {
                if (!existingParams.has(key)) {
                    existingParams.set(key, value);
                    hasChanges = true;
                }
            });

            if (hasChanges) {
                url.search = existingParams.toString();
                link.attr('href', url.toString());
            }
        }
    });
}

// Add UTM parameters to internal links when page loads
$(document).ready(function() {
    addUTMParamsToInternalLinks();
});

